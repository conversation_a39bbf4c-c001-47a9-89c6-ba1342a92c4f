<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\SystemLogResource;
use App\Models\SystemLog;
use App\Models\User\UserType;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SystemLogController extends Controller
{
    /**
     * Display a listing of system logs for administrators
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $query = SystemLog::query()
            ->with(['user:id,name,email,user_type_id'])
            ->orderBy('created_at', 'desc');

        // Filter by user type if specified
        if ($request->has('user_type')) {
            $query->where('user_type', $request->user_type);
        }

        // Filter by action type if specified
        if ($request->has('action_type')) {
            $query->where('action_type', $request->action_type);
        }

        // Filter by model name if specified
        if ($request->has('model_name')) {
            $query->where('model_name', 'like', '%' . $request->model_name . '%');
        }

        // Filter by user name if specified
        if ($request->has('user_name')) {
            $query->where('full_name', 'like', '%' . $request->user_name . '%');
        }

        // Filter by date range
        if ($request->has('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Filter by specific user ID
        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        $logs = $query->paginate($request->get('per_page', 50));

        return response()->json([
            'data' => SystemLogResource::collection($logs->items()),
            'pagination' => [
                'current_page' => $logs->currentPage(),
                'last_page' => $logs->lastPage(),
                'per_page' => $logs->perPage(),
                'total' => $logs->total(),
                'from' => $logs->firstItem(),
                'to' => $logs->lastItem(),
            ],
            'filters' => [
                'user_types' => UserType::TYPES,
                'action_types' => [
                    SystemLog::CREATE_ACTION,
                    SystemLog::UPDATE_ACTION,
                    SystemLog::DELETE_ACTION,
                    'login',
                    'logout'
                ]
            ]
        ]);
    }

    /**
     * Get statistics about system activity
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function statistics(Request $request): JsonResponse
    {
        $dateFrom = $request->get('date_from', now()->subDays(30)->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));

        $stats = [
            'total_activities' => SystemLog::whereBetween('created_at', [$dateFrom, $dateTo])->count(),
            'login_activities' => SystemLog::where('action_type', 'login')
                ->whereBetween('created_at', [$dateFrom, $dateTo])
                ->count(),
            'by_user_type' => SystemLog::selectRaw('user_type, COUNT(*) as count')
                ->whereBetween('created_at', [$dateFrom, $dateTo])
                ->whereNotNull('user_type')
                ->groupBy('user_type')
                ->get(),
            'by_action_type' => SystemLog::selectRaw('action_type, COUNT(*) as count')
                ->whereBetween('created_at', [$dateFrom, $dateTo])
                ->groupBy('action_type')
                ->get(),
            'most_active_users' => SystemLog::selectRaw('user_id, full_name, user_type, COUNT(*) as activity_count')
                ->whereBetween('created_at', [$dateFrom, $dateTo])
                ->whereNotNull('user_id')
                ->groupBy('user_id', 'full_name', 'user_type')
                ->orderBy('activity_count', 'desc')
                ->limit(10)
                ->get(),
            'daily_activity' => SystemLog::selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->whereBetween('created_at', [$dateFrom, $dateTo])
                ->groupBy('date')
                ->orderBy('date')
                ->get()
        ];

        return response()->json($stats);
    }

    /**
     * Show specific log entry details
     *
     * @param SystemLog $systemLog
     * @return JsonResponse
     */
    public function show(SystemLog $systemLog): JsonResponse
    {
        $systemLog->load(['user:id,name,email,user_type_id']);
        
        return response()->json([
            'data' => new SystemLogResource($systemLog)
        ]);
    }
}

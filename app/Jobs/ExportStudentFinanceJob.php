<?php

namespace App\Jobs;

use App\Exports\StudentFinanceExport;
use App\Mail\StudentFinanceExportMail;
use App\Models\Reestry\Administration\Administration;
use App\Models\User\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Carbon\Carbon;

class ExportStudentFinanceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $userMail;
    public $userName;

    public function __construct(public $userId)
    {
        $user = User::query()->find($this->userId);
        $this->userMail = $user->email;
        $this->userName = $user->name;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            // Generate filename with timestamp
            $timestamp = Carbon::now()->format('Y-m-d_H-i');
            $filename = "student_finance_export_{$timestamp}.xlsx";
            $filePath = "app/exports/{$filename}";
            $filePathDvaa = "exports/{$filename}";

            // Create the export
            $export = new StudentFinanceExport();

            // Store the file
            Excel::store($export, $filePath, 'public');

            // Get the full path
            $fullPath = Storage::path($filePathDvaa);

            // Send email with attachment
            Mail::to($this->userMail)->send(new StudentFinanceExportMail($fullPath, $filename, $this->userName));

            // Clean up the file after sending email
            Storage::delete($filePath);

        } catch (\Exception $e) {
            // Log the error
            \Log::error('Student Finance Export Job failed: ' . $e->getMessage());

            // Re-throw the exception to mark the job as failed
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        // Log the failure
        \Log::error('Student Finance Export Job failed permanently: ' . $exception->getMessage());

        // Optionally send notification to admin about failure
        try {
                \Log::info("Export failed for admin: {$this->userName}");
        } catch (\Exception $e) {
            \Log::error('Failed to notify admin about export failure: ' . $e->getMessage());
        }
    }
}

<?php

namespace App\Http\Requests\RegisterForms\Master;

use App\Models\RegisterForms\RegisterFormInfo;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
//        RegisterFormService::restrictedAccessForm($this->url,2);
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'identity_number_copy' => 'required|mimes:pdf,jpg,jpeg,png,doc,docx',
            'card_number' => 'required',
            'address' => 'required|string',
            'program_id' => 'required|exists:programs,id',
            //'flow_id' => 'required|exists:learn_years,id',
            'motivation_letter' => 'required|mimes:pdf,jpg,jpeg,png,doc,docx',
            'marks_paper' => 'required|mimes:pdf,jpg,jpeg,png,doc,docx',
            'english_level_id' => 'required|exists:english_levels,id',
            'certificates' => 'sometimes|array',
            'certificates.*' => 'sometimes|mimes:pdf,jpg,jpeg,png,doc,docx',
            'recommendations' => 'sometimes|array',
            'recommendations.*.person' => 'sometimes|string',
            'recommendations.*.phone' => 'sometimes|string',
            'infos' => 'sometimes|array',
            'infos.*.title' => 'sometimes|string',
            'educations' => 'required|array',
            'educations.*.university' => 'required|string',
            'educations.*.faculty' => 'required|string',
            'educations.*.start_date' => 'required|integer|min:1970|max:' . \Date::now()->year - 1,
            'educations.*.end_date' => 'required|integer|min:1970|max:' . \Date::now()->year,
            'first_name' => 'required|string',
            'last_name' => 'required|string',
            'interview_period' => 'required',
//            'identity_number' => [
//                'required',
//                'size:11',
//                function ($attribute, $value, $fail) {
//
//                    if (
//                        RegisterFormInfo::where(
//                            'registerable_type',
//                            '=',
//                            'App\Models\RegisterForms\MasterRegister'
//                        )->whereIdentityNumber($value)->exists()
//                    ) {
//                        $fail('თქვენს მიერ მითითებული პირადობის ნომრით ამ პროგრამაზე უკვე რეგისტრირებულია აპლიკანტი');
//                    }
//                }
//            ],
            'identity_number' => [
                'required',
                'regex:/^[0-9]+$/',
                'size:11'
            ],
            'phone' => 'required|integer',
//            'phone' => [
//                'required',
//                function ($attribute, $value, $fail) {
//                    if (
//                        RegisterFormInfo::where(
//                            'registerable_type',
//                            '=',
//                            'App\Models\RegisterForms\MasterRegister'
//                        )->wherePhone($value)->exists()
//                    ) {
//                        $fail('ამ ტელეფონით ამ პროგრამაზე უკვე რეგისტრირებულია აპლიკანტი');
//                    }
//                }
//            ],
            'email' => 'required|email',
            'gender' => 'required|boolean',
            'date_of_birth' => [
                'required',
                'date',
                'date_format:d-m-Y',
//                function ($attribute, $value, $fail) {
//                    if (now()->diffInYears($value) < 20) {
//                        $fail('მაგისტრატურაზე რეგისტრაცია შეუძლია მინიმუმ 20 წლის ადამიანს');
//                    }
//                },
            ],
            'exam_document' => 'nullable|mimes:pdf,jpg,jpeg,png,doc,docx',
            'finished_university_info' => 'nullable',
            'diploma_copy' => 'required|mimes:pdf,jpg,jpeg,png,doc,docx',
            'cv' => 'required|mimes:pdf,doc,docx'
        ];
    }

    public function messages()
    {
        return [
                'identity_number_copy.required' => 'პირადობის ასლის ატვირთვა სავალდებულოა',
                'identity_number_copy.mimes' => 'პირადობის ასლი უნდა იყოს ერთ-ერთი შემდეგი ფორმატის: pdf, jpg, jpeg, png, doc, docx.',
                'card_number.required' => 'პირადობის ბარათის ნომრის შეყვანა სავალდებულოა.',
                'address.required' => 'გთხოვთ, შეავსოთ ველი.',
                'address.string' => 'მისამართი უნდა იყოს ტექსტური.',
                'program_id.required' => 'გთხოვთ, შეავსოთ ველი.',
                'program_id.exists' => 'არჩეული პროგრამა არ არსებობს.',
                'motivation_letter.required' => 'გთხოვთ, შეავსოთ ველი.',
                'motivation_letter.mimes' => 'სამოტივაციო წერილი უნდა იყოს ერთ-ერთი შემდეგი ფორმატის: pdf, jpg, jpeg, png, doc, docx.',
                'marks_paper.required' => 'გთხოვთ, შეავსოთ ველი.',
                'marks_paper.mimes' => 'ნიშნების ფურცელი უნდა იყოს ერთ-ერთი შემდეგი ფორმატის: pdf, jpg, jpeg, png, doc, docx.',
                'english_level_id.required' => 'გთხოვთ, შეავსოთ ველი.',
                'english_level_id.exists' => 'არჩეული ინგლისური ენის დონე არ არსებობს.',
                'certificates.array' => 'სერტიფიკატები უნდა იყოს მასივი.',
                'certificates.*.mimes' => 'სერტიფიკატების თითოეული ფაილი უნდა იყოს ერთ-ერთი შემდეგი ფორმატის: pdf, jpg, jpeg, png, doc, docx.',
                'recommendations.array' => 'რეკომენდაციები უნდა იყოს მასივი.',
                'recommendations.*.person.string' => 'შევსებული მონაცემი უნდა იყოს ტექსტური.',
                'recommendations.*.phone.string' => 'რეკომენდაციის გამცემის ტელეფონი უნდა იყოს ტექსტური.',
                'infos.array' => 'ინფორმაცია უნდა იყოს მასივი.',
                'infos.*.title.string' => 'ინფორმაციის სათაური უნდა იყოს ტექსტური.',
                'educations.required' => 'გთხოვთ, შეავსოთ ველი.',
                'educations.array' => 'განათლება უნდა იყოს მასივი.',
                'educations.*.university.required' => 'მიუთითეთ უნივერსიტეტის სახელი.',
                'educations.*.university.string' => 'უნივერსიტეტის სახელი უნდა იყოს ტექსტური.',
                'educations.*.faculty.required' => 'მოთხოვნილია ფაკულტეტის სახელი.',
                'educations.*.faculty.string' => 'ფაკულტეტის სახელი უნდა იყოს ტექსტური.',
                'educations.*.start_date.required' => 'მიუთითეთ დაწყების თარიღი.',
                'educations.*.start_date.integer' => 'დაწყების თარიღი უნდა იყოს რიცხვი.',
                'educations.*.start_date.min' => 'დაწყების თარიღი არ შეიძლება იყოს 1970 წელზე ადრე.',
                'educations.*.start_date.max' => 'დაწყების თარიღი არ შეიძლება იყოს მიმდინარე წელზე გვიან.',
                'educations.*.end_date.required' => 'მიუთითეთ დასრულების თარიღი.',
                'educations.*.end_date.integer' => 'დასრულების თარიღი უნდა იყოს რიცხვი.',
                'educations.*.end_date.min' => 'დასრულების თარიღი არ შეიძლება იყოს 1970 წელზე ადრე.',
                'educations.*.end_date.max' => 'დასრულების თარიღი არ შეიძლება იყოს მიმდინარე წელზე მეტი.',
                'first_name.required' => 'გთხოვთ, შეავსოთ ველი.',
                'first_name.string' => 'სახელი უნდა იყოს ტექსტური.',
                'last_name.required' => 'გთხოვთ, შეავსოთ ველი.',
                'last_name.string' => 'გვარი უნდა იყოს ტექსტური.',
                'identity_number.required' => 'გთხოვთ, შეავსოთ ველი.',
                'identity_number.regex' => 'პირადობის ნომერი უნდა იყოს რიცხვები.',
                'identity_number.size' => 'პირადობის ნომერი უნდა შედგებოდეს 11 ციფრისგან.',
                'phone.required' => 'გთხოვთ, შეავსოთ ველი.',
                'phone.integer' => 'ტელეფონი უნდა იყოს რიცხვი.',
                'email.required' => 'გთხოვთ, შეავსოთ ველი.',
                'email.email' => 'ელ. ფოსტა უნდა იყოს ვალიდური.',
                'gender.required' => 'გთხოვთ, შეავსოთ ველი.',
                'gender.boolean' => 'სქესი უნდა იყოს ბულური მნიშვნელობის.',
                'date_of_birth.required' => 'გთხოვთ, შეავსოთ ველი.',
                'date_of_birth.date' => 'დაბადების თარიღი უნდა იყოს ვალიდური თარიღი.',
                'date_of_birth.date_format' => 'დაბადების თარიღი უნდა იყოს ფორმატით: დდ-თთ-წწწწ.',
                'date_of_birth.custom' => 'მაგისტრატურაზე რეგისტრაცია შეუძლია მინიმუმ 20 წლის ადამიანს.',
                'exam_document.mimes' => 'გამოცდის დოკუმენტი უნდა იყოს ერთ-ერთი შემდეგი ფორმატის: pdf, jpg, jpeg, png, doc, docx.',
                'finished_university_info.required' => 'მიუთითეთ დასრულებული უნივერსიტეტის ინფორმაცია.',
                'diploma_copy.sometimes' => 'გთხოვთ, შეავსოთ ველი.',
                'diploma_copy.mimes' => 'დიპლომის ასლი უნდა იყოს ერთ-ერთი შემდეგი ფორმატის: pdf, jpg, jpeg, png, doc, docx.',
                'diploma_copy.diploma_copy' => 'გთხოვთ, შეავსოთ ველი.',
                'cv.required' => 'მოთხოვნილია CV.',
                'cv.mimes' => 'CV უნდა იყოს ერთ-ერთი შემდეგი ფორმატის: pdf, doc, docx.',
        ];

    }

}

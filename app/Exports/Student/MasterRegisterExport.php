<?php

namespace App\Exports\Student;

use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;

class MasterRegisterExport implements FromCollection, WithHeadings, ShouldAutoSize
{
    public function __construct(public $data) {}

    public function collection()
    {
        return $this->data->map(function ($data, $index) {
            $interview = ' - ';
            if ($data->interview_period == 1)
            {
                $interview = '30 - 31 ივლისი';
            }
            elseif ($data->interview_period == 2)
            {
                $interview = '27 - 29 აგვისტო';
            }
            elseif ($data->interview_period == 3)
            {
                $interview = '9 - 10 სექტემბერი';
            }

            return [
                $index+1,
                $data->registerFormInfo?->last_name,
                $data->registerFormInfo?->first_name,
                $data->registerFormInfo?->email,
                $interview,
                Carbon::make($data->registerFormInfo?->date_of_birth)->format('d-m-Y'),
                $data->registerFormInfo?->identity_number,
                $data->registerFormInfo?->phone,
                $data->program?->name_ka,
                Carbon::make($data->created_at)->format('d-m-Y'),
            ];
        });
    }

    public function headings(): array
    {
        return [
            '#',
            'გვარი',
            'სახელი',
            'ელ-ფოსტა',
            'გასაუბრების თარიღი',
            'დაბადების თარიღი',
            'პირადობის ნომერი',
            'ტელეფონი',
            'პროგრამა',
            'რეგისტრაციის თარიღი',
        ];
    }
}

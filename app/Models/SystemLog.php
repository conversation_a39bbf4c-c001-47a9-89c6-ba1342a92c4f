<?php

namespace App\Models;

use App\Models\User\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SystemLog extends Model
{
    use HasFactory;

    protected $table = 'system_logs';

    const CREATE_ACTION = 'create';
    const UPDATE_ACTION = 'update';
    const DELETE_ACTION = 'delete';

    protected $fillable = [
        'action_data',
        'model_name',
        'action_type',
        'request_ip',
        'request_device',
    ];

    protected $casts = [
        'action_data' => 'array'
    ];

    protected static function boot(): void
    {
        parent::boot();

        static::creating(function ($model) {
            // Try to get user from web guard first, then api guard
            $userId = auth('web')->id() ?? auth('api')->id();
            $user = null;

            if ($userId) {
                $user = User::whereId($userId)->first();
                $model->user_id = $userId;
                $model->user_type = $user->userType?->title ?? null;
                $model->full_name = $user->name;
            }

            $model->request_ip = $model->request_ip ?? request()->ip();
            $model->request_device = $model->request_device ?? request()->header('User-Agent');
        });
    }

    /**
     * Get the user that performed the action
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}

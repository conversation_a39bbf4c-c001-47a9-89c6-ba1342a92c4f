<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\SystemLog;
use App\Models\User\User;
use App\Models\User\UserType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class AdminController extends Controller
{
    /**
     * Show admin login form
     */
    public function showLoginForm()
    {
        return view('admin.login');
    }

    /**
     * Handle admin login
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $user = User::where('email', $request->email)->first();

        if (!$user) {
            return back()->withErrors(['email' => 'მომხმარებელი ვერ მოიძებნა.']);
        }

        // Check if user is administrator
        if ($user->user_type_id !== UserType::ADMINISTRATION && !$user->is_super_admin) {
            return back()->withErrors(['email' => 'თქვენ არ გაქვთ ადმინისტრატორის უფლებები.']);
        }

        if (!Hash::check($request->password, $user->password)) {
            return back()->withErrors(['password' => 'პაროლი არასწორია.']);
        }

        Auth::guard('web')->login($user);

        // Log the login
        SystemLog::create([
            'action_type' => 'web_login',
            'model_name' => User::class,
            'action_data' => [
                'login_method' => 'web',
                'login_time' => now()->format('Y-m-d H:i:s'),
                'user_agent' => request()->header('User-Agent'),
                'ip_address' => request()->ip()
            ],
        ]);

        return redirect()->route('admin.logs');
    }

    /**
     * Show system logs page
     */
    public function logs(Request $request)
    {
        // Check if user is authenticated and is admin
        if (!Auth::guard('web')->check()) {
            return redirect()->route('admin.login');
        }

        $user = Auth::guard('web')->user();
        if ($user->user_type_id !== UserType::ADMINISTRATION && !$user->is_super_admin) {
            Auth::guard('web')->logout();
            return redirect()->route('admin.login')->withErrors(['error' => 'თქვენ არ გაქვთ ადმინისტრატორის უფლებები.']);
        }

        $query = SystemLog::query()
            ->with(['user:id,name,email,user_type_id'])
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('user_type')) {
            $query->where('user_type', $request->user_type);
        }

        if ($request->filled('action_type')) {
            $query->where('action_type', $request->action_type);
        }

        if ($request->filled('user_name')) {
            $query->where('full_name', 'like', '%' . $request->user_name . '%');
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $logs = $query->paginate(50);

        // Get filter options
        $userTypes = UserType::TYPES;
        $actionTypes = [
            'login' => 'ავტორიზაცია',
            'web_login' => 'ვებ ავტორიზაცია',
            'logout' => 'გასვლა',
            'create' => 'შექმნა',
            'update' => 'განახლება',
            'delete' => 'წაშლა',
            'view' => 'ნახვა',
            'search' => 'ძიება',
            'export' => 'ექსპორტი'
        ];

        // Get statistics
        $stats = [
            'total_today' => SystemLog::whereDate('created_at', today())->count(),
            'logins_today' => SystemLog::whereIn('action_type', ['login', 'web_login'])
                ->whereDate('created_at', today())->count(),
            'total_week' => SystemLog::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'most_active_today' => SystemLog::selectRaw('full_name, COUNT(*) as count')
                ->whereDate('created_at', today())
                ->whereNotNull('full_name')
                ->groupBy('full_name')
                ->orderBy('count', 'desc')
                ->limit(5)
                ->get()
        ];

        return view('admin.logs', compact('logs', 'userTypes', 'actionTypes', 'stats'));
    }

    /**
     * Logout admin
     */
    public function logout()
    {
        // Log the logout
        if (Auth::guard('web')->check()) {
            SystemLog::create([
                'action_type' => 'web_logout',
                'model_name' => User::class,
                'action_data' => [
                    'logout_method' => 'web',
                    'logout_time' => now()->format('Y-m-d H:i:s'),
                    'user_agent' => request()->header('User-Agent'),
                    'ip_address' => request()->ip()
                ],
            ]);
        }

        Auth::guard('web')->logout();
        return redirect()->route('admin.login')->with('success', 'წარმატებით გახვედით სისტემიდან.');
    }

    /**
     * Get log details via AJAX
     */
    public function getLogDetails($id)
    {
        $log = SystemLog::with(['user:id,name,email,user_type_id'])->findOrFail($id);
        
        return response()->json([
            'id' => $log->id,
            'user_name' => $log->full_name,
            'user_type' => $log->user_type,
            'user_email' => $log->user?->email,
            'action_type' => $log->action_type,
            'model_name' => class_basename($log->model_name),
            'action_data' => $log->action_data,
            'request_ip' => $log->request_ip,
            'request_device' => $log->request_device,
            'created_at' => $log->created_at->format('Y-m-d H:i:s'),
            'created_at_human' => $log->created_at->diffForHumans(),
        ]);
    }
}

# ადმინისტრატორის ლოგინის ტესტირება

## ცვლილებები რომ გავაკეთეთ

### 1. AdminController-ში
- `Auth::login($user)` → `Auth::guard('web')->login($user)`
- `Auth::check()` → `Auth::guard('web')->check()`
- `Auth::user()` → `Auth::guard('web')->user()`
- `Auth::logout()` → `Auth::guard('web')->logout()`

### 2. SystemLog Model-ში
- გავაუმჯობესეთ boot() მეთოდი რომ მუშაობდეს ორივე guard-თან (web და api)

### 3. Blade Template-ში
- `Auth::user()` → `Auth::guard('web')->user()`

### 4. Routes-ში
- დავამატეთ `middleware('web')` admin routes-ზე

## ტესტირების ნაბიჯები

### 1. ავტორიზაციის გვერდზე გადასვლა
```
GET /admin/login
```

### 2. ავტორიზაცია
- შეიყვანეთ ადმინისტრატორის ელ-ფოსტა და პაროლი
- დააჭირეთ "შესვლა"

### 3. შედეგი
- წარმატებული ავტორიზაციის შემდეგ უნდა გადამისამართდეთ `/admin/logs`-ზე
- ლოგების გვერდი უნდა ჩაიტვირთოს სტატისტიკით

### 4. ლოგირების შემოწმება
- ავტორიზაციის შემდეგ system_logs ცხრილში უნდა შეიქმნას ჩანაწერი:
  - `action_type`: 'web_login'
  - `model_name`: 'App\Models\User\User'
  - `user_id`: თქვენი user ID
  - `action_data`: JSON ობიექტი login ინფორმაციით

## შესაძლო პრობლემები და გადაწყვეტები

### 1. "Session store not set on request"
**გადაწყვეტა**: დარწმუნდით რომ web middleware group გამოიყენება routes-ზე

### 2. "CSRF token mismatch"
**გადაწყვეტა**: დარწმუნდით რომ login form-ში არის `@csrf` directive

### 3. "Method does not exist"
**გადაწყვეტა**: დარწმუნდით რომ იყენებთ `Auth::guard('web')` ვებ routes-ისთვის

### 4. სესია არ ინახება
**გადაწყვეტა**: შეამოწმეთ:
- `.env` ფაილში `SESSION_DRIVER=file`
- `storage/framework/sessions` დირექტორია არსებობს და writable-ია

## მოსალოდნელი შედეგი

1. **ავტორიზაციის გვერდი**: ლამაზი UI ფორმით
2. **წარმატებული ლოგინი**: გადამისამართება ლოგების გვერდზე
3. **ლოგების გვერდი**: სტატისტიკა, ფილტრები, ლოგების ცხრილი
4. **ლოგირება**: ავტორიზაცია ლოგირდება system_logs-ში
5. **გასვლა**: უსაფრთხო logout და გადამისამართება login გვერდზე

## დამატებითი შენიშვნები

- ვებ ავტორიზაცია იყენებს სესია-ბაზირებულ authentication-ს
- API ავტორიზაცია კვლავ იყენებს Passport token-ებს
- ორივე სისტემა მუშაობს პარალელურად
- SystemLog მოდელი ავტომატურად ამოიცნობს რომელი guard გამოიყენება

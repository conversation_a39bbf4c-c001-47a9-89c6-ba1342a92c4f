<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SystemLogResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'user_name' => $this->full_name,
            'user_type' => $this->user_type,
            'user_email' => $this->user?->email,
            'action_type' => $this->action_type,
            'action_type_display' => $this->getActionTypeDisplay(),
            'model_name' => $this->model_name,
            'model_display' => $this->getModelDisplay(),
            'action_data' => $this->action_data,
            'action_summary' => $this->getActionSummary(),
            'request_ip' => $this->request_ip,
            'request_device' => $this->request_device,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'created_at_human' => $this->created_at?->diffFor<PERSON><PERSON>s(),
        ];
    }

    /**
     * Get human readable action type
     */
    private function getActionTypeDisplay(): string
    {
        return match($this->action_type) {
            'create' => 'შექმნა',
            'update' => 'განახლება',
            'delete' => 'წაშლა',
            'login' => 'ავტორიზაცია',
            'logout' => 'გასვლა',
            default => $this->action_type
        };
    }

    /**
     * Get human readable model name
     */
    private function getModelDisplay(): string
    {
        $modelName = class_basename($this->model_name);
        
        return match($modelName) {
            'User' => 'მომხმარებელი',
            'Student' => 'სტუდენტი',
            'Lecturer' => 'ლექტორი',
            'Administration' => 'ადმინისტრაცია',
            'Syllabus' => 'სილაბუსი',
            'Assignment' => 'დავალება',
            'Lecture' => 'ლექცია',
            'Program' => 'პროგრამა',
            'Curriculum' => 'კურიკულუმი',
            default => $modelName
        };
    }

    /**
     * Get action summary based on action type and data
     */
    private function getActionSummary(): string
    {
        $modelDisplay = $this->getModelDisplay();
        $actionDisplay = $this->getActionTypeDisplay();
        
        if ($this->action_type === 'login') {
            return "მომხმარებელმა {$this->full_name} შეასრულა ავტორიზაცია";
        }
        
        if ($this->action_type === 'logout') {
            return "მომხმარებელმა {$this->full_name} დატოვა სისტემა";
        }
        
        if ($this->action_type === 'create') {
            return "მომხმარებელმა {$this->full_name} შექმნა ახალი {$modelDisplay}";
        }
        
        if ($this->action_type === 'update') {
            $changedFields = $this->getChangedFields();
            if ($changedFields) {
                return "მომხმარებელმა {$this->full_name} განაახლა {$modelDisplay} - შეცვლილი ველები: {$changedFields}";
            }
            return "მომხმარებელმა {$this->full_name} განაახლა {$modelDisplay}";
        }
        
        if ($this->action_type === 'delete') {
            return "მომხმარებელმა {$this->full_name} წაშალა {$modelDisplay}";
        }
        
        return "{$this->full_name} - {$actionDisplay} - {$modelDisplay}";
    }

    /**
     * Get changed fields for update actions
     */
    private function getChangedFields(): string
    {
        if ($this->action_type !== 'update' || !is_array($this->action_data)) {
            return '';
        }
        
        $fields = array_keys($this->action_data);
        $translatedFields = array_map(function($field) {
            return match($field) {
                'name' => 'სახელი',
                'email' => 'ელ-ფოსტა',
                'password' => 'პაროლი',
                'first_name' => 'სახელი',
                'last_name' => 'გვარი',
                'phone' => 'ტელეფონი',
                'status_id' => 'სტატუსი',
                'program_id' => 'პროგრამა',
                'school_id' => 'სკოლა',
                'title' => 'სათაური',
                'description' => 'აღწერა',
                'grade' => 'შეფასება',
                default => $field
            };
        }, $fields);
        
        return implode(', ', $translatedFields);
    }
}

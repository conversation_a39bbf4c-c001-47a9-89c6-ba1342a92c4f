<?php

namespace App\Http\Controllers\API\V1\RegisterForms;

use App\Exports\Student\MasterRegisterExport;
use App\Mail\MasterRegister as MasterRegisterMail;
use App\Filters\ProgramFilter;
use App\Filters\Student\ApplicantsFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\RegisterForms\Master\StoreRequest;
use App\Http\Requests\RegisterForms\Master\UpdateRequest;
use App\Http\Resources\MasterRegisterResource;
use App\Models\EnglishLevel;
use App\Models\Master\MasterCertificate;
use App\Models\Master\MasterEducation;
use App\Models\Master\MasterRecommendation;
use App\Models\Reestry\LearnYear;
use App\Models\Reestry\Program\Program;
use App\Models\Reestry\Student\StudentStatusList;
use App\Models\RegisterForms\MasterRegister;
use App\Services\ImageService;
use App\Services\MasterRegisterService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Exception;
use Str;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;
use function response;

class MasterRegisterController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:master.index'])->only(['index']);
        $this->middleware(['permission:master.destroy'])->only(['destroy']);
    }

    const fileNames = [
        'identity_number_copy',
        'motivation_letter',
        'marks_paper',
        'cv',
        'diploma_copy',
        'exam_document'
    ];

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */

    public function index(ApplicantsFilter $filter)
    {
        $paginator = MasterRegister::filter($filter)->with([
            'registerFormInfo',
            'masterCertificates',
            'masterRecommendations',
            'masterLanguages',
            'educations',
            'program:id,name_ka',
        ])->orderByDesc('id')->paginate(30);

        $data = $paginator->toArray();
        $programFilter = new ProgramFilter();
        $additionalData = [
            'status' => StudentStatusList::pluck('name_ka', 'id'),
            'programs' => Program::filter($programFilter)->whereAcademicDegreeId(2)->get(),
            'englishLevels' => EnglishLevel::all()
        ];
        $data['additional'] = $additionalData;

        return response()->json($data);
    }

    /**
     * @param StoreRequest $request
     * @param ImageService $imageService
     * @return MasterRegisterResource
     */

    public function store(
        StoreRequest          $request,
        ImageService          $imageService,
        MasterRegisterService $masterRegisterService
    ): MasterRegisterResource
    {
        $identityNumber = $request->identity_number;
        $validated = $request->validated();
        //$validated['program_id'] = 1;
        $validated['flow_id'] = LearnYear::where('program_id', $validated['program_id'])
            ->where('active', 1)
            ->value('id');
        foreach (self::fileNames as $fileName) {
            $validated[$fileName] = $imageService
                ->upload(
                    $request->{$fileName},
                    'master-register/' .
                    $identityNumber . '/' .
                    $fileName);
        }
        $masterRegister = MasterRegister::create($validated);
        $program = Program::query()->find($validated['program_id']);

        Mail::to($request->email)->send(new MasterRegisterMail(
            $validated['first_name'],
            $validated['last_name'],
            $program->name_ka,
            now(),
            $masterRegister->id,
        ));

        $masterRegister->registerFormInfo()->create($request->validated());
        if ($request->has('certificates')) {
            foreach ($request->certificates as $certificate) {
                MasterCertificate::create([
                    'master_register_id' => $masterRegister->id,
                    'title' => $imageService->upload(
                        $certificate,
                        '/master-register/' .
                        $identityNumber .
                        '/certificates')
                ]);
            }
        }

        if ($request->has('recommendations')) {
            foreach ($request->recommendations as $recommendation) {
                MasterRecommendation::create([
                    'master_register_id' => $masterRegister->id,
                    'person' => $recommendation['person'],
                    'phone' => $recommendation['phone']
                ]);
            }
        }

        foreach ($request->educations as $education) {
            (new MasterRegisterService())
                ->storeEducations($masterRegister->id, $education);
        }

        if ($request->has('infos')) {
            foreach ($request->infos as $info) {
                $masterRegisterService->storeInfo(
                    $masterRegister->id,
                    $info
                );
            }
        }
        return new MasterRegisterResource($masterRegister->load([
            'program',
            'masterCertificates',
            'masterRecommendations',
            'infos',
            'educations'
        ]));

    }

    public function show($masterRegister): MasterRegisterResource
    {
//        return new MasterRegisterResource(MasterRegister::find($masterRegister)
//            ->load([
//                'program',
//                'masterCertificates',
//                'masterLanguages',
//                'masterRecommendations'
//            ]));
    }

    /**
     * @param UpdateRequest $request
     * @param int $masterRegister
     * @param ImageService $imageService
     * @return MasterRegisterResource
     */

    public function update(UpdateRequest $request, int $masterRegister, ImageService $imageService): MasterRegisterResource
    {
//        $validated = $request->validated();
//        $masterRegister = MasterRegister::find($masterRegister);
//        $identityNumber = $request->identity_number;
//        foreach (self::fileNames as $fileName) {
//            if ($request->has($fileName)) {
//                $isFillable = (new MasterRegister)->isFillable($fileName);
//                $path = $isFillable ? $masterRegister->{$fileName}
//                    : $masterRegister->registerFormInfo->{$fileName};
//                File::delete('storage' . $path);
//                $validated[$fileName] = $imageService
//                    ->upload($request->{$fileName}, 'master-register/' . $identityNumber . '/');
//            }
//        }
//        MasterEducation::whereMasterRegisterId($masterRegister->id)->delete();
//        foreach ($request->education as $education) {
//            MasterEducation::create([
//                'master_register_id' => $masterRegister->id,
//                'university' => $education->university_name,
//                'faculty' => $education->faculty,
//                'start_date' => $education->start_date,
//                'end_date' => $education->end_date
//            ]);
//        }
//        MasterCertificate::whereMasterRegisterId($masterRegister->id)->delete();
//        if ($request->has('certificates')) {
//            foreach ($request->certificates as $certificate) {
//                MasterCertificate::create([
//                    'master_register_id' => $masterRegister->id,
//                    'title' => $imageService->upload($certificate, '/elbooks/')
//                ]);
//            }
//        }
//        MasterRecommendation::whereMasterRegisterId($masterRegister->id)->delete();
//        if ($request->has('recommendations')) {
//            foreach ($request->recommendations as $recommendation) {
//                MasterRecommendation::create([
//                    'master_register_id' => $masterRegister->id,
//                    'person' => $recommendation->person,
//                    'phone' => $recommendation->phone
//                ]);
//            }
//        }
//        return new MasterRegisterResource($masterRegister);
    }

    /**
     * @param integer $masterRegister
     * @return Response
     */

    public function destroy(int $masterRegister): Response
    {
        $masterRegister = MasterRegister::findOrFail($masterRegister);

        if ($masterRegister->registerFormInfo) {
            $masterRegister->registerFormInfo->delete();
        }

        $masterRegister->masterCertificates->each(function ($certificate) {
            $certificate->delete();
        });

        $masterRegister->masterRecommendations->each(function ($recommendation) {
            $recommendation->delete();
        });

        $masterRegister->masterLanguages->each(function ($language) {
            $language->delete();
        });

        $masterRegister->educations->each(function ($education) {
            $education->delete();
        });

        $masterRegister->infos->each(function ($info) {
            $info->delete();
        });

        $masterRegister->delete();
        return response(null, ResponseAlias::HTTP_NO_CONTENT);
    }

    /**
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function export(ApplicantsFilter $filter): string
    {
        $applicants = MasterRegister::filter($filter)->with([
            'registerFormInfo',
            'masterCertificates',
            'masterRecommendations',
            'masterLanguages',
            'educations',
            'program:id,name_ka',
        ])->orderByDesc('id')->get();

        $fileName = 'master-applicants-' . date('d-m-Y') . '.xlsx';
        Excel::store(
            new MasterRegisterExport($applicants),
            $fileName,
            config('excel.storage_registry')
        );

        return 'excel/' . config('excel.storage_registry') . '/' . $fileName;

        //return Excel::download(new MasterRegisterExport($applicants), 'users.xlsx');
    }
}


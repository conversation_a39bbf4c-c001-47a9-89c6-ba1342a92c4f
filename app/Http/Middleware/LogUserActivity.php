<?php

namespace App\Http\Middleware;

use App\Models\SystemLog;
use App\Models\User\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LogUserActivity
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        // Only log for authenticated users
        if (Auth::check()) {
            $this->logActivity($request, $response);
        }

        return $response;
    }

    /**
     * Log user activity
     */
    private function logActivity(Request $request, $response): void
    {
        // Skip logging for certain routes to avoid noise
        $skipRoutes = [
            'api/auth/check',
            'api/system-logs',
            'api/notifications',
            'api/heartbeat'
        ];

        $path = $request->path();
        foreach ($skipRoutes as $skipRoute) {
            if (str_contains($path, $skipRoute)) {
                return;
            }
        }

        // Skip if it's not a meaningful action (GET requests to index pages)
        if ($request->isMethod('GET') && !$this->isImportantGetRequest($request)) {
            return;
        }

        $user = Auth::user();
        $actionType = $this->determineActionType($request);
        $actionData = $this->buildActionData($request, $response);

        SystemLog::create([
            'action_type' => $actionType,
            'model_name' => $this->determineModelName($request),
            'action_data' => $actionData,
        ]);
    }

    /**
     * Determine if this is an important GET request worth logging
     */
    private function isImportantGetRequest(Request $request): bool
    {
        $path = $request->path();
        
        // Log specific resource views
        $importantPatterns = [
            '/students/\d+$/',
            '/lecturers/\d+$/',
            '/administrations/\d+$/',
            '/syllabuses/\d+$/',
            '/assignments/\d+$/',
            '/programs/\d+$/',
            '/export',
            '/download',
            '/search'
        ];

        foreach ($importantPatterns as $pattern) {
            if (preg_match($pattern, $path)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Determine action type based on request
     */
    private function determineActionType(Request $request): string
    {
        $method = $request->method();
        $path = $request->path();

        if (str_contains($path, 'search')) {
            return 'search';
        }

        if (str_contains($path, 'export') || str_contains($path, 'download')) {
            return 'export';
        }

        return match($method) {
            'POST' => 'create',
            'PUT', 'PATCH' => 'update',
            'DELETE' => 'delete',
            'GET' => 'view',
            default => 'action'
        };
    }

    /**
     * Determine model name from request
     */
    private function determineModelName(Request $request): string
    {
        $path = $request->path();
        
        // Extract model from API path
        if (preg_match('/api\/(?:v1\/)?([^\/]+)/', $path, $matches)) {
            $resource = $matches[1];
            
            return match($resource) {
                'students' => 'App\Models\Reestry\Student\Student',
                'lecturers' => 'App\Models\Reestry\Lecturer\Lecturer',
                'administrations' => 'App\Models\Reestry\Administration\Administration',
                'syllabuses' => 'App\Models\Syllabus\Syllabus',
                'assignments' => 'App\Models\Assignment',
                'programs' => 'App\Models\Reestry\Program\Program',
                'users' => 'App\Models\User\User',
                'curriculum' => 'App\Models\Curriculum\Curriculum',
                'lectures' => 'App\Models\Lectures\Lecture',
                default => 'System'
            };
        }

        return 'System';
    }

    /**
     * Build action data array
     */
    private function buildActionData(Request $request, $response): array
    {
        $data = [
            'method' => $request->method(),
            'path' => $request->path(),
            'timestamp' => now()->format('Y-m-d H:i:s'),
        ];

        // Add query parameters for GET requests
        if ($request->isMethod('GET') && $request->query()) {
            $data['query_params'] = $request->query();
        }

        // Add request data for POST/PUT requests (excluding sensitive data)
        if (in_array($request->method(), ['POST', 'PUT', 'PATCH'])) {
            $requestData = $request->except(['password', 'password_confirmation', '_token']);
            if (!empty($requestData)) {
                $data['request_data'] = $requestData;
            }
        }

        // Add response status
        if (method_exists($response, 'getStatusCode')) {
            $data['response_status'] = $response->getStatusCode();
        }

        return $data;
    }
}

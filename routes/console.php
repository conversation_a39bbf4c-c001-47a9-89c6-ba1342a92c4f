<?php

use App\Console\Commands\AssignmentDuplicateClearCommand;
use App\Console\Commands\FinanceReportCommand;
use App\Console\Commands\FinanceStatusUpdateCommand;
use App\Console\Commands\FixDustedSurveyDBCommand;
use App\Console\Commands\InactiveStudentAttendances;
use App\Console\Commands\StudentPointCalculationCommand;
use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

/*
|--------------------------------------------------------------------------
| Console Routes
|--------------------------------------------------------------------------
|
| This file is where you may define all of your Closure based console
| commands. Each Closure is bound to a command instance allowing a
| simple approach to interacting with each command's IO methods.
|
*/

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

/*
|--------------------------------------------------------------------------
| Task Scheduling
|--------------------------------------------------------------------------
|
| Here you may define all of your scheduled tasks. Laravel's scheduler
| allows you to fluently and expressively define your command schedule
| within your Laravel application itself.
|


*/




// Financial status updates
Schedule::command(FinanceStatusUpdateCommand::class)->dailyAt('16:00');

// Inactive student attendances
Schedule::command(InactiveStudentAttendances::class)->dailyAt('22:00');

// Move grades to archive and close subjects
Schedule::command(StudentPointCalculationCommand::class)->dailyAt('23:55');

// Removing duplicated survey data
Schedule::command(FixDustedSurveyDBCommand::class)->dailyAt('02:00');

// Clean duplicate student assignments
Schedule::command(AssignmentDuplicateClearCommand::class)->dailyAt('03:00');

// Auto mail sender for Lika – finance report
Schedule::command(FinanceReportCommand::class)->weeklyOn(5, '16:10'); // 5 = Friday

// Check contract expirations daily at 08:00
Schedule::command('contracts:check-expirations')->dailyAt('08:00');

// Survey activation (currently disabled)
// Schedule::command('survey:activate')->dailyAt('23:00');

# სისტემის ლოგების მონიტორინგი

## მიმოხილვა

ეს ფუნქციონალი საშუალებას აძლევს ადმინისტრატორებს მონიტორინგი გაუწიონ ყველა იუზერის აქტივობას სისტემაში.

## ფუნქციები

### 1. ავტომატური ლოგირება
- **ლოგინ/ლოგაუტ**: ყველა ავტორიზაცია და გასვლა ლოგირდება
- **მოდელების ცვლილებები**: ყველა CRUD ოპერაცია (შექმნა, განახლება, წაშლა)
- **API მოთხოვნები**: მნიშვნელოვანი API calls-ები ლოგირდება

### 2. ფილტრაცია
- იუზერის ტიპის მიხედვით (ადმინისტრატორი, ლექტორი, სტუდენტი)
- მოქმედების ტიპის მიხედვით (შექმნა, განახლება, წაშლა, ლოგინი)
- მოდელის სახელის მიხედვით
- იუზერის სახელის მიხედვით
- თარიღის დიაპაზონის მიხედვით

### 3. სტატისტიკა
- საერთო აქტივობის რაოდენობა
- ლოგინების რაოდენობა
- აქტივობა იუზერის ტიპის მიხედვით
- ყველაზე აქტიური იუზერები
- ყოველდღიური აქტივობის გრაფიკი

## API Endpoints

### ლოგების სია
```
GET /api/administration/system-logs
```

**პარამეტრები:**
- `user_type` - იუზერის ტიპი
- `action_type` - მოქმედების ტიპი
- `model_name` - მოდელის სახელი
- `user_name` - იუზერის სახელი
- `date_from` - თარიღი დან
- `date_to` - თარიღი მდე
- `user_id` - კონკრეტული იუზერის ID
- `per_page` - გვერდზე ჩანაწერების რაოდენობა (default: 50)

### სტატისტიკა
```
GET /api/administration/system-logs/statistics
```

**პარამეტრები:**
- `date_from` - თარიღი დან (default: 30 დღის წინ)
- `date_to` - თარიღი მდე (default: დღეს)

### კონკრეტული ლოგის დეტალები
```
GET /api/administration/system-logs/{id}
```

## მონაცემთა ბაზის სტრუქტურა

### system_logs ცხრილი
- `id` - უნიკალური იდენტიფიკატორი
- `user_id` - იუზერის ID
- `user_type` - იუზერის ტიპი
- `full_name` - იუზერის სრული სახელი
- `action_type` - მოქმედების ტიპი
- `model_name` - მოდელის სახელი
- `action_data` - მოქმედების დეტალები (JSON)
- `request_ip` - IP მისამართი
- `request_device` - მოწყობილობის ინფორმაცია
- `created_at` - შექმნის თარიღი

## გამოყენება

### ადმინისტრატორისთვის
1. შედით სისტემაში ადმინისტრატორის უფლებებით
2. გადადით `/api/administration/system-logs` endpoint-ზე
3. გამოიყენეთ ფილტრები საჭირო ინფორმაციის მოსაძებნად
4. იხილეთ სტატისტიკა `/api/administration/system-logs/statistics` endpoint-ზე

### მაგალითები

#### ყველა ლოგინის ნახვა ბოლო კვირის განმავლობაში
```
GET /api/administration/system-logs?action_type=login&date_from=2024-01-20&date_to=2024-01-27
```

#### სტუდენტების აქტივობის ნახვა
```
GET /api/administration/system-logs?user_type=Student
```

#### კონკრეტული იუზერის ყველა მოქმედება
```
GET /api/administration/system-logs?user_id=123
```

## უსაფრთხოება

- მხოლოდ ადმინისტრატორებს აქვთ წვდომა ლოგების ნახვაზე
- ყველა მოთხოვნა ავტორიზაციას საჭიროებს
- მგრძნობიარე ინფორმაცია (პაროლები) არ ლოგირდება

## ტექნიკური დეტალები

### Middleware
- `LogUserActivity` - ავტომატურად ლოგავს ყველა მნიშვნელოვან მოქმედებას
- `auth.administrator` - უზრუნველყოფს მხოლოდ ადმინისტრატორების წვდომას

### Resource
- `SystemLogResource` - ფორმატირებს ლოგების გამოსატანად

### Controller
- `SystemLogController` - მართავს ყველა API endpoint-ს

## შენიშვნები

1. ლოგები ავტომატურად იქმნება ყველა მნიშვნელოვანი მოქმედებისას
2. სისტემა ინახავს IP მისამართს და მოწყობილობის ინფორმაციას
3. ლოგები შეიცავს დეტალურ ინფორმაციას თუ რა შეიცვალა
4. ყველა ლოგი ქართულ ენაზეა ფორმატირებული

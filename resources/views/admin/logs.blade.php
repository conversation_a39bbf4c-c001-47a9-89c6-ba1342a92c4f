<!DOCTYPE html>
<html lang="ka">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>სისტემის ლოგები - GIPA Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 2rem;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        .stats-card h4 {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .filter-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        .table thead th {
            background: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #495057;
        }
        .badge {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
        }
        .btn-details {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            color: white;
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
        }
        .btn-details:hover {
            color: white;
            transform: translateY(-1px);
        }
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
        }
        .pagination .page-link {
            border-radius: 8px;
            margin: 0 2px;
            border: none;
            color: #667eea;
        }
        .pagination .page-item.active .page-link {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-shield-alt me-2"></i>
                GIPA Admin - სისტემის ლოგები
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-2"></i>{{ Auth::user()->name }}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ route('admin.logout') }}">
                            <i class="fas fa-sign-out-alt me-2"></i>გასვლა
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Statistics Row -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <h4>{{ $stats['total_today'] }}</h4>
                    <p class="mb-0"><i class="fas fa-calendar-day me-2"></i>დღევანდელი აქტივობა</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h4>{{ $stats['logins_today'] }}</h4>
                    <p class="mb-0"><i class="fas fa-sign-in-alt me-2"></i>დღევანდელი ლოგინები</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h4>{{ $stats['total_week'] }}</h4>
                    <p class="mb-0"><i class="fas fa-calendar-week me-2"></i>კვირეული აქტივობა</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h4>{{ $stats['most_active_today']->count() }}</h4>
                    <p class="mb-0"><i class="fas fa-users me-2"></i>აქტიური იუზერები დღეს</p>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filter-card">
            <h5 class="mb-3"><i class="fas fa-filter me-2"></i>ფილტრები</h5>
            <form method="GET" action="{{ route('admin.logs') }}">
                <div class="row">
                    <div class="col-md-2">
                        <label class="form-label">იუზერის ტიპი</label>
                        <select name="user_type" class="form-select">
                            <option value="">ყველა</option>
                            @foreach($userTypes as $type)
                                <option value="{{ $type }}" {{ request('user_type') == $type ? 'selected' : '' }}>
                                    {{ $type }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">მოქმედების ტიპი</label>
                        <select name="action_type" class="form-select">
                            <option value="">ყველა</option>
                            @foreach($actionTypes as $key => $value)
                                <option value="{{ $key }}" {{ request('action_type') == $key ? 'selected' : '' }}>
                                    {{ $value }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">იუზერის სახელი</label>
                        <input type="text" name="user_name" class="form-control" value="{{ request('user_name') }}" placeholder="ძიება...">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">თარიღი დან</label>
                        <input type="date" name="date_from" class="form-control" value="{{ request('date_from') }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">თარიღი მდე</label>
                        <input type="date" name="date_to" class="form-control" value="{{ request('date_to') }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>ძიება
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Logs Table -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>სისტემის ლოგები</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="logsTable">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>იუზერი</th>
                                <th>ტიპი</th>
                                <th>მოქმედება</th>
                                <th>მოდელი</th>
                                <th>IP მისამართი</th>
                                <th>თარიღი</th>
                                <th>მოქმედებები</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($logs as $log)
                                <tr>
                                    <td>{{ $log->id }}</td>
                                    <td>
                                        <strong>{{ $log->full_name ?? 'უცნობი' }}</strong>
                                        @if($log->user?->email)
                                            <br><small class="text-muted">{{ $log->user->email }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        @if($log->user_type)
                                            <span class="badge bg-primary">{{ $log->user_type }}</span>
                                        @else
                                            <span class="badge bg-secondary">უცნობი</span>
                                        @endif
                                    </td>
                                    <td>
                                        @php
                                            $actionClass = match($log->action_type) {
                                                'login', 'web_login' => 'bg-success',
                                                'logout', 'web_logout' => 'bg-warning',
                                                'create' => 'bg-info',
                                                'update' => 'bg-primary',
                                                'delete' => 'bg-danger',
                                                'view' => 'bg-secondary',
                                                'search' => 'bg-dark',
                                                'export' => 'bg-success',
                                                default => 'bg-light text-dark'
                                            };
                                            $actionText = $actionTypes[$log->action_type] ?? $log->action_type;
                                        @endphp
                                        <span class="badge {{ $actionClass }}">{{ $actionText }}</span>
                                    </td>
                                    <td>
                                        <small>{{ class_basename($log->model_name) }}</small>
                                    </td>
                                    <td>
                                        <small>{{ $log->request_ip }}</small>
                                    </td>
                                    <td>
                                        <small>{{ $log->created_at->format('Y-m-d H:i:s') }}</small>
                                        <br><small class="text-muted">{{ $log->created_at->diffForHumans() }}</small>
                                    </td>
                                    <td>
                                        <button class="btn btn-details btn-sm" onclick="showLogDetails({{ $log->id }})">
                                            <i class="fas fa-eye me-1"></i>დეტალები
                                        </button>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">ლოგები ვერ მოიძებნა</p>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $logs->appends(request()->query())->links() }}
                </div>
            </div>
        </div>
    </div>

    <!-- Log Details Modal -->
    <div class="modal fade" id="logDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-info-circle me-2"></i>ლოგის დეტალები</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="logDetailsContent">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">იტვირთება...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        // Initialize DataTable
        $(document).ready(function() {
            $('#logsTable').DataTable({
                "paging": false,
                "searching": false,
                "info": false,
                "ordering": true,
                "order": [[0, "desc"]],
                "language": {
                    "emptyTable": "ლოგები ვერ მოიძებნა",
                    "zeroRecords": "ლოგები ვერ მოიძებნა"
                }
            });
        });

        // Show log details
        function showLogDetails(logId) {
            $('#logDetailsModal').modal('show');
            $('#logDetailsContent').html(`
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">იტვირთება...</span>
                    </div>
                </div>
            `);

            $.get(`/admin/logs/${logId}/details`)
                .done(function(data) {
                    let actionData = '';
                    if (data.action_data && typeof data.action_data === 'object') {
                        actionData = '<pre class="bg-light p-3 rounded">' + JSON.stringify(data.action_data, null, 2) + '</pre>';
                    }

                    $('#logDetailsContent').html(`
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-user me-2"></i>იუზერის ინფორმაცია</h6>
                                <p><strong>სახელი:</strong> ${data.user_name || 'უცნობი'}</p>
                                <p><strong>ელ-ფოსტა:</strong> ${data.user_email || 'უცნობი'}</p>
                                <p><strong>ტიპი:</strong> ${data.user_type || 'უცნობი'}</p>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-cog me-2"></i>მოქმედების ინფორმაცია</h6>
                                <p><strong>მოქმედება:</strong> ${data.action_type}</p>
                                <p><strong>მოდელი:</strong> ${data.model_name}</p>
                                <p><strong>თარიღი:</strong> ${data.created_at}</p>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6><i class="fas fa-network-wired me-2"></i>ტექნიკური ინფორმაცია</h6>
                                <p><strong>IP მისამართი:</strong> ${data.request_ip}</p>
                                <p><strong>მოწყობილობა:</strong> <small>${data.request_device}</small></p>
                            </div>
                        </div>
                        ${actionData ? `
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6><i class="fas fa-code me-2"></i>მოქმედების დეტალები</h6>
                                ${actionData}
                            </div>
                        </div>
                        ` : ''}
                    `);
                })
                .fail(function() {
                    $('#logDetailsContent').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            ლოგის დეტალების ჩატვირთვა ვერ მოხერხდა
                        </div>
                    `);
                });
        }

        // Auto refresh every 30 seconds
        setInterval(function() {
            if (!$('#logDetailsModal').hasClass('show')) {
                location.reload();
            }
        }, 30000);
    </script>
</body>
</html>

<?php

namespace App\Models\RegisterForms;

use App\Models\EnglishLevel;
use App\Models\Master\MasterCertificate;
use App\Models\Master\MasterEducation;
use App\Models\Master\MasterLanguage;
use App\Models\Master\MasterRecommendation;
use App\Models\Reestry\Program\Program;
use App\Models\RegisterForms\Master\MasterRegisterInfo;
use App\Traits\HasFilters;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;

/**
 * App\Models\RegisterForms\MasterRegister
 *
 * @property int $id
 * @property string $identity_number_copy
 * @property string $exam_document
 * @property string $finished_university_info
 * @property string $diploma_copy
 * @property string $marks_paper
 * @property string $cv
 * @property string $motivation_letter
 * @property string|null $acknowledge_info
 * @property int $english_level_id
 * @property string $card_number
 * @property string $address
 * @property int $program_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|MasterEducation[] $educations
 * @property-read int|null $educations_count
 * @property-read EnglishLevel|null $englishLevel
 * @property-read \Illuminate\Database\Eloquent\Collection|MasterRegisterInfo[] $infos
 * @property-read int|null $infos_count
 * @property-read \Illuminate\Database\Eloquent\Collection|MasterCertificate[] $masterCertificates
 * @property-read int|null $master_certificates_count
 * @property-read \Illuminate\Database\Eloquent\Collection|MasterLanguage[] $masterLanguages
 * @property-read int|null $master_languages_count
 * @property-read \Illuminate\Database\Eloquent\Collection|MasterRecommendation[] $masterRecommendations
 * @property-read int|null $master_recommendations_count
 * @property-read Program|null $program
 * @property-read \App\Models\RegisterForms\RegisterFormInfo|null $registerFormInfo
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegister newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegister newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegister query()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegister whereAcknowledgeInfo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegister whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegister whereCardNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegister whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegister whereCv($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegister whereDiplomaCopy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegister whereEnglishLevelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegister whereExamDocument($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegister whereFinishedUniversityInfo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegister whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegister whereIdentityNumberCopy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegister whereMarksPaper($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegister whereMotivationLetter($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegister whereProgramId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegister whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegister filter(\App\Filters\QueryFilters $filters)
 * @property int $flow_id
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegister whereFlowId($value)
 * @mixin \Eloquent
 */
class MasterRegister extends Model
{
    use HasFactory,HasFilters;

    protected $fillable = [
        'identity_number_copy',
        'card_number',
        'address',
        'program_id',
        'flow_id',
        'motivation_letter',
        'marks_paper',
        'english_level_id',
        'exam_document',
        'finished_university_info',
        'diploma_copy',
        'cv',
        'interview_period'
    ];

    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    public function masterCertificates(): HasMany
    {
        return $this->hasMany(MasterCertificate::class);
    }

    public function masterRecommendations(): HasMany
    {
        return $this->hasMany(MasterRecommendation::class);
    }

    public function masterLanguages(): HasMany
    {
        return $this->hasMany(MasterLanguage::class);
    }

    public function registerFormInfo(): MorphOne
    {
        return $this->morphOne(RegisterFormInfo::class, 'registerable');
    }

    public function englishLevel(): BelongsTo
    {
        return $this->belongsTo(EnglishLevel::class);
    }

    public function educations(): HasMany
    {
        return $this->hasMany(
            MasterEducation::class,
            'master_register_id',
            'id'
        );
    }

    public function infos(): HasMany
    {
        return $this->hasMany(
            MasterRegisterInfo::class,
            'master_register_id',
            'id'
        );
    }
}

<?php

use App\Http\Controllers\API\V1\Reestry\Student\StudentController;
use App\Http\Controllers\Web\AdminController;
use App\Models\Syllabus\Syllabus;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Support\Facades\Route;

Route::get('/test', function () {
    $syllabus = Syllabus::with([
        'learnYear',
        'status',
        'semester',
        'lecturers.lecturerContactTimes',
        'weeks',
        'assignments',
        'prerequisites',
        'academicDegree',
        'learningOutcomes',
        'methods'
    ])->first();

    return Pdf::loadView('pdf.syllabus', [
        'syllabus' => $syllabus,
    ])->stream();
});

//Route::get('finance', function () {
//    $logs = DB::table('finance_student_status_logs')
//        ->where('status_id', 1)
//        ->whereBetween('created_at', [
//            Carbon::parse('2024-12-24 19:50:47.000000'),
//            Carbon::parse('2024-12-24 23:56:47.000000')
//        ])
//        ->get();
//
//    foreach ($logs as $log) {
//        echo $log->student_id . ',' . PHP_EOL;
//    }
//});
Route::get('/student-agreement/{id}', [StudentController::class, 'studentAgreement']);
Route::get('/student-agreement/enrolled/{id}', [StudentController::class, 'enrolledStudentAgreement']);

// Admin Web Routes
Route::prefix('admin')->name('admin.')->group(function () {
    Route::get('/login', [AdminController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AdminController::class, 'login'])->name('login.post');
    Route::get('/logout', [AdminController::class, 'logout'])->name('logout');
    Route::get('/logs', [AdminController::class, 'logs'])->name('logs');
    Route::get('/logs/{id}/details', [AdminController::class, 'getLogDetails'])->name('logs.details');
});

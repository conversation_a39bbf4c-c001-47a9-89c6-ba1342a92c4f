# ადმინისტრატორის ვებ ინტერფეისი - სისტემის ლოგების მონიტორინგი

## მიმოხილვა

ეს არის ვებ-ბაზირებული ინტერფეისი ადმინისტრატორებისთვის, რომელიც საშუალებას აძლევს მონიტორინგი გაუწიონ ყველა იუზერის აქტივობას სისტემაში.

## ფუნქციები

### 1. უსაფრთხო ავტორიზაცია
- ელ-ფოსტა და პაროლით ავტორიზაცია
- მხოლოდ ადმინისტრატორებისთვის წვდომა
- ავტომატური გადამისამართება ლოგების გვერდზე
- ლოგინის ლოგირება

### 2. ლოგების მონიტორინგი
- რეალურ დროში ლოგების ნახვა
- ავტომატური განახლება ყოველ 30 წამში
- დეტალური ინფორმაცია თითოეული ლოგისთვის
- ფილტრაცია და ძიება

### 3. სტატისტიკა
- დღევანდელი აქტივობის რაოდენობა
- ლოგინების რაოდენობა
- კვირეული აქტივობა
- აქტიური იუზერების რაოდენობა

### 4. ფილტრაცია
- იუზერის ტიპის მიხედვით
- მოქმედების ტიპის მიხედვით
- იუზერის სახელის მიხედვით
- თარიღის დიაპაზონის მიხედვით

## URL მისამართები

### ავტორიზაცია
```
GET /admin/login - ავტორიზაციის გვერდი
POST /admin/login - ავტორიზაციის დამუშავება
```

### ლოგების მონიტორინგი
```
GET /admin/logs - ლოგების მთავარი გვერდი
GET /admin/logs/{id}/details - კონკრეტული ლოგის დეტალები (AJAX)
```

### გასვლა
```
GET /admin/logout - სისტემიდან გასვლა
```

## გამოყენება

### 1. ავტორიზაცია
1. გადადით `/admin/login` მისამართზე
2. შეიყვანეთ ადმინისტრატორის ელ-ფოსტა და პაროლი
3. დააჭირეთ "შესვლა" ღილაკს
4. წარმატებული ავტორიზაციის შემდეგ გადამისამართდებით ლოგების გვერდზე

### 2. ლოგების ნახვა
- ლოგები ნაჩვენებია ცხრილის სახით
- ყოველი ლოგი შეიცავს: ID, იუზერი, ტიპი, მოქმედება, მოდელი, IP, თარიღი
- "დეტალები" ღილაკზე დაჭერით იხილავთ სრულ ინფორმაციას

### 3. ფილტრაცია
- გამოიყენეთ ზედა ფილტრები საჭირო ლოგების მოსაძებნად
- შეგიძლიათ ერთდროულად რამდენიმე ფილტრის გამოყენება
- დააჭირეთ "ძიება" ღილაკს ფილტრების გამოსაყენებლად

### 4. სტატისტიკა
- ზედა ნაწილში ნაჩვენებია ძირითადი სტატისტიკა
- სტატისტიკა ავტომატურად განახლდება

## ფუნქციონალური თავისებურებები

### ავტომატური განახლება
- გვერდი ავტომატურად განახლდება ყოველ 30 წამში
- განახლება არ ხდება თუ ღია არის დეტალების მოდალი

### რესპონსიული დიზაინი
- ინტერფეისი ადაპტირებულია ყველა ეკრანის ზომაზე
- მობილურ მოწყობილობებზე ოპტიმიზებული

### ინტერაქტიული ელემენტები
- ლოგების დეტალები იხსნება მოდალურ ფანჯარაში
- AJAX-ით ჩატვირთვა სწრაფი რესპონსისთვის
- ლოდინის ინდიკატორები

## უსაფრთხოება

### ავტორიზაციის შემოწმება
- ყოველი გვერდი ამოწმებს ავტორიზაციას
- არა-ადმინისტრატორები ავტომატურად გადამისამართდებიან ლოგინის გვერდზე

### სესიის მართვა
- უსაფრთხო სესიის მართვა Laravel-ის მეშვეობით
- ავტომატური გასვლა უსაფრთხოებისთვის

### ლოგირება
- ყველა ვებ ავტორიზაცია ლოგირდება
- IP მისამართი და მოწყობილობის ინფორმაცია ინახება

## ტექნიკური დეტალები

### Frontend ტექნოლოგიები
- Bootstrap 5.3.0 - UI ფრეიმვორკი
- Font Awesome 6.0.0 - იკონები
- jQuery 3.7.0 - JavaScript ბიბლიოთეკა
- DataTables - ცხრილების ფუნქციონალი

### Backend
- Laravel Blade Templates
- AdminController - ლოგიკის მართვა
- SystemLog Model - მონაცემებთან მუშაობა

### სტილები
- მოდერნული გრადიენტები
- ანიმაციები და ტრანზიციები
- კარტების დიზაინი
- რესპონსიული ლეიაუტი

## შეცდომების მართვა

### ავტორიზაციის შეცდომები
- არასწორი ელ-ფოსტა ან პაროლი
- არა-ადმინისტრატორის მცდელობა
- ვალიდაციის შეცდომები

### AJAX შეცდომები
- ლოგის დეტალების ჩატვირთვის შეცდომა
- ქსელის კავშირის პრობლემები

## მომავალი გაუმჯობესებები

1. **ექსპორტი** - ლოგების Excel/PDF ფორმატში ექსპორტი
2. **რეალურ დროში განახლება** - WebSocket-ებით
3. **გრაფიკული ანალიტიკა** - Chart.js-ით
4. **მეტი ფილტრები** - IP მისამართი, მოწყობილობა
5. **ბალკ ოპერაციები** - მრავალი ლოგის ერთდროული მართვა

## მხარდაჭერა

ტექნიკური პრობლემების შემთხვევაში დაუკავშირდით სისტემის ადმინისტრატორს.

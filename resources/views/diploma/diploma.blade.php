<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Diploma Supplement - GIPA</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 6px;
            padding: 0 3px;
        }

        h1, h2, h3 {
            color: #000;
            font-size: 10pt;
        }

        /*table {*/
        /*    width: 100%;*/
        /*    border-collapse: collapse;*/
        /*    margin-bottom: 20px;*/
        /*    font-size: 10pt;*/
        /*}*/

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 10pt;
        }
        th, td {
            padding: 10px;
            border: 1px solid #bdc3c7;
            vertical-align: top;
        }


        th {
            text-align: left;
        }

        .two-column td:first-child,
        .two-column th:first-child {
            width: 40%;
        }

        .two-column td:last-child,
        .two-column th:last-child {
            width: 60%;
        }

        .three-column td,
        .three-column th {
            width: 33.33%;
        }

        .logo-head {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 50px;
            gap: 10px;
        }

        .flex-h {
            text-align: right;
            font-weight: bold;
        }

        .intro-container {
            text-align: justify;
            font-size: 10pt;
            display: flex;
            justify-content: center;
        }

        .logo-image {
            width: 140px;
            height: auto;
        }

        .footer-logo {
            width: 700px;
            height: auto;
        }

        .just {
            text-align: justify;
        }
        .title-style {
            padding-bottom: 40px;
            font-size: 10pt;
        }
        .img-cont {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 50px;
        }
    </style>
</head>
<body>
<table style="width: 100%; margin-bottom: 30px; border: none; border-collapse: collapse;">
    <tr>
        <td style="width: 150px; vertical-align: top; border: none;">
            <img src="{{ asset('images/diploma/logo.png') }}" alt="logo" style="width: 140px; height: auto;">
        </td>
        <td style="text-align: right; font-weight: bold; font-size: 11pt; border: none;">
            <p style="margin-top: 20px !important;">ა(ა)იპ ჯიპა - საქართველოს საზოგადოებრივ საქმეთა ინსტიტუტი</p>
            <p>GIPA № {{ $student->diploma_number ?? '' }} დიპლომის დანართი</p>
        </td>
    </tr>
</table>





<div class="intro-container">
    <p>დიპლომის დანართის მიზანია მიაწოდოს მიუკერძოებელი მონაცემები საერთაშორისო „გამჭვირვალობისა“ და კვალიფიკაციების (დიპლომების, ხარისხების, სერტიფიკატების და ა.შ.) სამართლიანი, აკადემიური და პროფესიული, ცნობის/აღიარების გასაუმჯობესებლად. მისი მიზანია, აღწეროს კვალიფიკაციის ორიგინალ დოკუმენტში, რომელსაც დაერთვის ეს დანართი, დასახელებული პირის მიერ გავლილი და წარმატებით დასრულებული საგანმანათლებლო პროგრამის ხასიათი, დონე, კონტექსტი, შინაარსი და სტატუსი. აღწერა თავისუფალი უნდა იყოს შეფასებითი დასკვნებისაგან, ეკვივალენტობის მტკიცებისა ან აღიარების შესახებ შეთავაზებისაგან. დიპლომის დანართის ეს ნიმუში შემუშავებულია ევროკომისიის, ევროსაბჭოსა და UNESCO-ს მიერ.</p>
</div>

<h2>1. კვალიფიკაციის მფლობელის შესახებ საიდენტიფიკაციო ინფორმაცია</h2>
<table class="two-column">
    <tr><td width="40%">1.1 გვარი:</td><td>{{ $student->surname ?? '' }}</td></tr>
    <tr><td width="40%">1.2 სახელ(ებ)ი:</td><td>{{ $student->name ?? ''}}</td></tr>
    <tr><td width="40%">1.3 დაბადების თარიღი:</td><td>{{ $student->birthday?->format('d-m-Y') ?? ''}}</td></tr>
    <tr><td width="40%">1.4 სტუდენტის საიდენტიფიკაციო ნომერი ან კოდი:</td><td>{{ $student->personal_id ?? '' }}</td></tr>
</table>

<h2>2. კვალიფიკაციის საიდენტიფიკაციო ინფორმაცია</h2>
<table class="two-column">
    <tr><td width="40%">2.1 კვალიფიკაციის დასახელება:</td><td>{{ $student->program->additional->kvalifikacia ?? '' }}</td></tr>
    <tr><td width="40%">2.2 სწავლების ძირითადი სფერო:</td><td>{{ $student->program->additional->kreditebi ?? '' }}</td></tr>
    <tr><td width="40%">2.3 კვალიფიკაციის მიმნიჭებელი დაწესებულება:</td><td class="just">ა(ა)იპ - „ჯიპა - საქართველოს საზოგადოებრივ
            საქმეთა ინსტიტუტი” არის უნივერსიტეტი, რომელმაც 2024 წელს გაიარა სსიპ -
            განათლების ხარისხის განვითარების ეროვნული
            ცენტრის გარე ხარისხის უზრუნველყოფის
            პროცესი (ავტორიზაცია). აღნიშნული ცენტრი
            2019 წლიდან არის უმაღლესი განათლების
            ხარისხის უზრუნველყოფის ევროპული ქსელის
            (ENQA) წევრი.</td></tr>
    <tr><td>2.4 სწავლების ენა:</td><td>იხ. 2.3 პუნქტი (არ განსხვავდება 2.3 პუნქტისგან)</td></tr>
    <tr><td>2.5 სწავლის/გამოცდის ენა (ენები):</td><td>{{ $student->program->additional->swavlis_ena ?? '' }}</td></tr>
</table>

<h2>3. ინფორმაცია კვალიფიკაციის დონისა და ხანგრძლივობის შესახებ</h2>
<table class="two-column">
    <tr><td width="40%">3.1 კვალიფიკაციის დონე:</td><td>{{ $student->program->additional->kvalifikaciis_done ?? '' }}</td></tr>
    <tr><td width="40%">3.2 პროგრამის ხანგრძლივობა:</td><td>{{ $student->program->additional->kreditebi ?? '' }}</td></tr>
    <tr><td width="40%">3.3 დაშვების წინაპირობა:</td><td>{{ $student->program->additional->dashvebis_winapiroba ?? '' }}</td></tr>
</table>

<h2>4. ინფორმაცია პროგრამასა და მიღწეულ შედეგებზე</h2>
<table class="two-column">
    <tr><td width="40%">4.1 სწავლის ფორმა:</td><td>სწავლა სრული დატვირთვით</td></tr>
    <tr><td width="40%">4.2 პროგრამის სწავლის შედეგები:</td><td>{{ $student->program->additional->swavlis_shedegebi ?? '' }}</td></tr>
</table>
<div class="title-style" >
    4.3 პროგრამის დეტალები, მოპოვებული ინდივიდუალური კრედიტები და მიღებული ნიშნები/შეფასებები (თუ არსებობს, უნდა დაერთოს ოფიციალური ნიშნების ფურცელი (ტრანსკრიპტი)
</div>
<table class="uniform-table two-column">
    <thead>
    <tr>
        <th></th>
        <th>სასწავლო კურსის დასახელება</th>
        <th>კრედიტი</th>
        <th>შეფასება</th>
    </tr>
    </thead>
    <tbody>
{{--        {{ $student->syllabi }}--}}
        @php
            $histories = \App\Models\Reestry\Student\StudentSyllabusHistory::studentHistories($student->id);
            $count = 0;
            foreach ($histories as $history)
            {
                if(round($history->point) >= 51)
                {
                    $count += $history->credits ?? $history->syllabus->credits;
                }
            }
        @endphp
        @foreach($histories as $key=>$grade)
            @php
                $point = round($grade->point);
                if ($point >= 91) {
                    $pointMarker = 'A';
                }elseif($point>=81){
                    $pointMarker = 'B';
                }elseif($point>=71){
                    $pointMarker = 'C';
                }elseif($point>=61){
                    $pointMarker = 'D';
                }elseif($point>=51){
                    $pointMarker = 'E';
                }else{
                    $pointMarker = 'F';
                }
            @endphp
            <tr style="height: 10px !important;">
                <td>{{ $key + 1 }}</td>
                <td>{{ $grade->syllabus->name }}</td>
                <td>{{ $grade->credits ?? $grade->syllabus->credits }}</td>
                <td>{{ "$point / $pointMarker" }}</td>

            </tr>
        @endforeach

    <tr>
        <td></td>
        <td>
            <strong>კრედიტების ჯამი:</strong>
        </td>
        <td>
            {{ $count }}
        </td>
        <td></td>
    </tr>
    <tr>
        <td></td>
        <td>
            <strong>GPA:</strong>
        </td>
        <td>
            {{ $student->gpa ?? '' }}
        </td>
        <td></td>
    </tr>


{{--    <tr><td></td><td><strong>GPA</strong></td><td></td></tr>--}}
    </tbody>
</table>
<p class="intr-p">4.4 შეფასებების სისტემა და, თუ შესაძლებელია, შეფასებების განაწილების ცხრილი: {{ $student->program->additional->programis_dasaxeleba ?? '' }}</p>
<table class="uniform-table">
    <tr><td>
            <h3>სასწავლო კურსის მაქსიმალური შეფასებაა 100 ქულა:</h3>
            <p>{{ $student->program->additional->other_name ?? '' }} პროგრამას აქვს სტუდენტების ცოდნის შეფასების მრავალკომპონენტიანი სისტემა, რაც განპირობებულია შუალედური კომპონენტებისა და დასკვნითი გამოცდის ქულებით. სტუდენტის ცოდნის შეფასების სისტემა შეესაბამება საქართველოში შეფასების ცენტრალური სისტემით დადგენილ ხუთი სახის დადებით შეფასებას:</p>
            <p >(A) ფრიადი - 91-100 ქულა; (B) ძალიან კარგი - 81-90 ქულა; (C) კარგი - 71 – 80 ქულა; (D) დამაკმაყოფილებელი – 61 – 70 ქულა; (E) საკმარისი - 51 – 60 ქულა; (FX) ვერ ჩააბარა - 41 – 50 ქულა, რაც ნიშნავს, რომ სტუდენტს ჩასაბარებლად მეტი მუშაობა სჭირდება და ეძლევა დამოუკიდებელი მუშაობით დამატებით გამოცდაზე ერთხელ გასვლის უფლება; (F) ჩაიჭრა - 0 – 40 ქულა, რაც ნიშნავს, რომ სტუდენტის მიერ ჩატარებული სამუშაო არ არის საკმარისი და მას საგანი ახლიდან აქვს შესასწავლი</p>
            <p >აკადემიური მოსწრების საშუალო მაჩვენებელი:</p>
            <p>A = 4 <br/>B = 3,2 <br/>C = 2,4 <br/>D = 1,6 <br/>E = 0,8</p>
        </td>
    </tr>
</table>
<table class="uniform-table two-column">
    <tbody>
    <tr><td width="40%">4.5 კვალიფიკაციის სრული კლასიფიკაცია:</td>
        <td>შესაბამისი ინფრომაცია არ არსებობს</td>
    </tr>
    </tbody>
</table>

<h2>5. ინფორმაცია კვალიფიკაციის ფუნქციის შესახებ</h2>
<table class="two-column">
    <tr><td width="40%">5.1 დაშვება სწავლის შემდგომ საფეხურზე:</td><td>{{ $student->program->additional->shemdgomi_safexuri  ?? ''}}</td></tr>
    <tr><td width="40%">5.2 დაშვება რეგულირებად პროფესიაზე:</td><td>შესაბამისი ინფორმაცია არ არსებობს</td></tr>
</table>

<h2>6. დამატებითი ინფორმაცია</h2>
<table class="two-column">
    <tr><td width="40%">6.1 დამატებითი ინფორმაცია:</td><td>დამატებითი ინფორმაცია არ არსებობს</td></tr>
    <tr><td width="40%">6.2 ინფორმაციის წყარო:</td><td>www.gipa.ge</td></tr>
</table>

<h2>7. დანართის დამოწმება</h2>
<div style="font-size: 13px">7.1 თარიღი: {{ $student->diploma_taken_date?->format('d/m/Y') ?? '' }} <br>7.2 ხელმოწერა: იოსელიანი მარინე<br>7.3 თანამდებობა: რექტორი<br>7.4 ოფიციალური შტამპი ან ბეჭედი:</div>
<br>
<h2>8. ინფორმაცია უმაღლესი განათლების ეროვნული სისტემის შესახებ</h2>


<div class="intro-container">
    <p>
        2005 წლიდან საქართველოში შემოღებულ იქნა აკადემიური უმაღლესი განათლების სამსაფეხურიანი სისტემა. ამ სისტემაზე სრული გადასვლა განხორციელდა 2007-2008 სასწავლო წლის დასაწყისიდან.  უმაღლესი განათლების საფეხურის დასამთავრებლად აუცილებელია შესაბამისი რაოდენობის კრედიტების დაგროვება. უმაღლესი განათლება საფეხურების მიხედვით მოიცავს ბაკალავრიატის (ეროვნული კვალიფიკაციების ჩარჩოს მე-6 დონე), მაგისტრატურისა (ეროვნული კვალიფიკაციების ჩარჩოს მე-7 დონე) და დოქტორანტურის (ეროვნული კვალიფიკაციების ჩარჩოს მე-8 დონე) პროგრამებს. გარდა ამისა, არსებობს ინტეგრირებული პროგრამები (მედიცინისა და სტომატოლოგიის ერთსაფეხურიანი, მასწავლებლის მომზადების ინტეგრირებული საბაკალავრო-სამაგისტრო, ვეტერინარიის ინტეგრირებული სამაგისტრო საგანმანათლებლო), რომლებიც აერთიანებენ საბაკალავრო და სამაგისტრო დონეებს და თავისი სწავლის შედეგით შეესაბამებიან სამაგისტრო დონეს. ბაკალავრიატისა და ინტეგრირებული პროგრამებზე დაიშვებიან სრული ზოგადი განათლების დამადასტურებელი დოკუმენტის ან მასთან გათანაბრებული დოკუმენტის მქონე პირები, ერთიანი ეროვნული გამოცდების გავლის საფუძველზე. ასევე საქართველოს კანონმდებლობით გათვალისწინებულია უმაღლესი განათლების პირველ საფეხურთან დაკავშირებული ან მის ფარგლებში არსებული მოკლე ციკლის საგანმანათლებლო პროგრამების განხორცილების შესაძლებლობა. საბაკალავრო საგანმანათლებლო პროგრამის მოცულობაა არანაკლებ 180-240 ECTS კრედიტი. მაგისტრატურის საგანმანათლებლო პროგრამის მოცულობაა არანაკლებ 60-120 ECTS კრედიტი, გარდა რეგულირებადი საგანმანათლებლო პროგრამებისა.  რეგულირებადი საგანმანათლებლო პროგრამებისთვის სახელმწიფო ადგენს სპეციალურ სააკრედიტაციო მოთხოვნებს. რეგულირებად პროფესიაში საქმიანობის დაწყების წინაპირობა, შესაბამისი კვალიფიკაციის არსებობის გარდა, შესაძლოა იყოს  სახელმწიფო სასერტიფიკატო  გამოცდის ჩაბარება. დოქტორანტურის საგანმანათლებლო პროგრამის ხანგრძლივობაა არანაკლებ 3 წელი და მისი სასწავლო კომპონენტის მოცულობაა არაუმეტეს 60 ECTS კრედიტი.

    </p>
    <p>მასწავლებლის მომზადების საგანმანათლებლო პროგრამა და სპეციალური მასწავლებლის მომზადების საგანმანათლებლო პროგრამა - ხორციელდება ეროვნული კვალიფიკაციების ჩარჩოს მე-6 დონეზე. მასწავლებლის მომზადების/სპეციალური  მასწავლებლის  მომზადების საგანმანათლებლო პროგრამა მოიცავს არანაკლებ 60 ECTS კრედიტს და ხორციელდება არანაკლებ 1 სასწავლო წლის განმავლობაში.  მასწავლებლის მომზადების საგანმანათლებლო პროგრამა ეროვნული სასწავლო გეგმით გათვალისწინებული საგნის/საგნობრივი ჯგუფის მიმართულებით ხორციელდება და ბაკალავრიატის საგანმანათლებლო პროგრამის ფარგლებში ან ბაკალავრიატის საგანმანათლებლო პროგრამისა და მასწავლებლის მომზადების ინტეგრირებული საბაკალავრო-სამაგისტრო საგანმანათლებლო პროგრამისაგან დამოუკიდებლად. სპეციალური მასწავლებლის მომზადების საგანმანათლებლო პროგრამა ხორციელდება ბაკალავრიატის საგანმანათლებლო პროგრამისა და მასწავლებლის მომზადების ინტეგრირებული საბაკალავრო-სამაგისტრო საგანმანათლებლო პროგრამისგან დამოუკიდებლად. მასწავლებლის მომზადების/სპეციალური მასწავლებლის მომზადების საგანმანათლებლო პროგრამის დასრულების შემდეგ გაიცემა   მასწავლებლის მომზადების/სპეციალური მასწავლებლის მომზადების სერტიფიკატი, რომელიც პირს საგნობრივი გამოცდის ჩაბარების შემდგომ,  მასწავლებლად დასაქმების შესაძლებლობას აძლევს. თუ საგნის/საგნობრივი ჯგუფის ძირითად სწავლის სფეროში შემავალი ბაკალავრიატის საგანმანათლებლო პროგრამა მასწავლებლის მომზადების საგანმანათლებლო პროგრამას მოიცავს, დიპლომსა და დიპლომის დანართში მიეთითება აგრეთვე ზოგადი განათლების შესაბამისი საფეხურის საგნის/საგნების სწავლების უფლება. </p>
    <p>ქართულ ენაში მომზადების საგანმანათლებლო პროგრამა –  პროგრამა მიზნად ისახავს ქართულენოვან საგანმანათლებლო პროგრამებზე ჩარიცხულ იმ პირთათვის ვისთვისაც ქართული ენა მშობლიური არ არის, ქართულ ენაში უნარ-ჩვევებისა და ცოდნის (წერა, კითხვა, მოსმენა, საუბარი) იმ დონეზე შეძენას, რომელიც აუცილებელია ბაკალავრიატის საფეხურზე ან ინტეგრირებულ პროგრამაზე სწავლის გასაგრძელებლად. ქართულ ენაში მომზადების საგანმანათლებლო პროგრამა 60-კრედიტიანია. მისი გავლა შესაძლებელია მხოლოდ პირველი სასწავლო წლის განმავლობაში. პროგრამის დასრულების შემდეგ უმაღლესი საგანმანათლებლო დაწესებულება გასცემს მისი დასრულების დამადასტურებელ სერტიფიკატს. პროგრამის განხორციელების ვალდებულება აქვთ მხოლოდ სახელმწიფოს მიერ დაფუძნებულ საგანმანათლებლო დაწესებულებებს. სტუდენტები ვალდებული არიან აღნიშნული პროგრამის დასრულების შემდეგ ბაკალავრიატის, ან ინტეგრირებული საგანმანათლებლო პროგრამით სწავლა განაგრძონ ქართულ ენაზე.  </p>
    <p>ქართულ ენაში მომზადების საგანმანათლებლო პროგრამა –  პროგრამა მიზნად ისახავს ქართულენოვან საგანმანათლებლო პროგრამებზე ჩარიცხულ იმ პირთათვის ვისთვისაც ქართული ენა მშობლიური არ არის, ქართულ ენაში უნარ-ჩვევებისა და ცოდნის (წერა, კითხვა, მოსმენა, საუბარი) იმ დონეზე შეძენას, რომელიც აუცილებელია ბაკალავრიატის საფეხურზე ან ინტეგრირებულ პროგრამაზე სწავლის გასაგრძელებლად. ქართულ ენაში მომზადების საგანმანათლებლო პროგრამა 60-კრედიტიანია. მისი გავლა შესაძლებელია მხოლოდ პირველი სასწავლო წლის განმავლობაში. პროგრამის დასრულების შემდეგ უმაღლესი საგანმანათლებლო დაწესებულება გასცემს მისი დასრულების დამადასტურებელ სერტიფიკატს. პროგრამის განხორციელების ვალდებულება აქვთ მხოლოდ სახელმწიფოს მიერ დაფუძნებულ საგანმანათლებლო დაწესებულებებს. სტუდენტები ვალდებული არიან აღნიშნული პროგრამის დასრულების შემდეგ ბაკალავრიატის, ან ინტეგრირებული საგანმანათლებლო პროგრამით სწავლა განაგრძონ ქართულ ენაზე.  </p>





    <p class="intr-p just">უმაღლესი განათლების მეორე საფეხურზე, მაგისტრატურაში, დაიშვებიან ბაკალავრის ან მასთან გათანაბრებული აკადემიური ხარისხის   მქონე პირები.  სამაგისტრო პროგრამებზე მიღების წინაპირობაა საერთო სამაგისტრო გამოცდები. მიღების დამატებითი პირობები განისაზღვრება უმაღლესი საგანმანათლებლო დაწესებულების მიერ.   </p>
    <p class="intr-p just">უსდ- უფლებამოსილია შეიმუშაოს, მაგისტრატურის საგანმანათლებლო პროგრამა (გარდა რეგულირებადი საგანმანათლებლო პროგრამებისა), რომელიც მოიცავს არანაკლებ 60 კრედიტს.  არანაკლებ 60 კრედიტიანი მაგისტრატურის საგანმანათლებლო პროგრამის  შემუშავება და განხორციელება შესაძლებელია ბიზნესისა და ადმინისტრირების ვიწრო სფეროში მაგისტრის აკადემიური ხარისხის მოპოვების მიზნებისთვის, მათ შორის, აღმასრულებელი ბიზნესის ადმინისტრირების მაგისტრის (EMBA) კვალიფიკაციის მოპოვების მიზნებისთვისაც. პროგრამაზე დაშვების წინაპირობას წარმოადგენს ბაკალავრიატის ან მასთან გათანაბრებული აკადემიური ხარისხის ქონა.  აღნიშნულ პროგრამაზე ჩარიცხვის წესს და პირობებს განსაზღვრავს შესაბამისი უმაღლესი საგანმანათლებლო დაწესებულება. აღმასრულებელი ბიზნესის ადმინისტრირების  მაგისტრის (EMBA) კვალიფიკაციის მოპოვების მიზნებისთვის, დამატებით, სავალდებულოა არანაკლებ 5-წლიანი პროფესიული გამოცდილება მართვის/ადმინისტრირების სფეროში.    </p>
    <p class="intr-p just">ვეტერინარის მომზადების საგანმანათლებლო პროგრამა არის ვეტერინარიის ინტეგრირებული სამაგისტრო საგანმანათლებლო პროგრამისაგან დამოუკიდებელი, შესაბამის სტანდარტზე დაყრდნობით შემუშავებული საგანმანათლებლო პროგრამა, რომლის სწავლის შედეგებიც შეესაბამება ეროვნული კვალიფიკაციების ჩარჩოს მე-7 დონისათვის განსაზღვრულ განზოგადებულ სწავლის შედეგებს. ვეტერინარის მომზადების საგანმანათლებლო პროგრამის დასრულების შედეგად გაიცემა ვეტერინარის სერტიფიკატი. აღნიშნული პროგრამაზე დაიშვებიან მხოლოდ ვეტერინარიის ბაკალავრის კვალიფიკაციის მქონე პირები და კურსდამთავრებულებს უფლება აქვთ სწავლა გააგრძელონ განათლების შემდგომ საფეხურზე − დოქტორანტურაში.  </p>
    <p class="intr-p just">დოქტორანტურაში  დაიშვებიან მაგისტრის აკადემიური ხარისხის ან მასთან გათანაბრებული კვალიფიკაციის მქონე პირები. გარდა, იმ შემთხვევისა, თუ პირს მაგისტრის ხარისხის მოპოვებული აქვს არანაკლებ 60 კრედიტის მოცულობის მქონე მაგისტრატურის საგანმანათლებლო პროგრამის ფარგლებში. დოქტორანტურაში მიღების წინაპირობა განისაზღვრება უმაღლესი საგანმანათლებლო დაწესებულების მიერ.  </p>


    <h2>უმაღლესი საგანმანათლებლო დაწესებულებების სახეები</h2>
    <p class="intr-p just">საქართველოში კანონმდებლობით არსებობს შემდეგი სახის უმაღლესი საგანმანათლებლო დაწესებულებები:  

        კოლეჯი – უმაღლესი საგანმანათლებლო დაწესებულება, რომელიც ახორციელებს მხოლოდ ბაკალავრიატის საგანმანათლებლო პროგრამას; 

        უნივერსიტეტი – უმაღლესი საგანმანათლებლო დაწესებულება (უსდ), რომელიც ახორციელებს ვეტერინარიის ინტეგრირებულ სამაგისტრო საგანმანათლებლო პროგრამას,  მასწავლებლის მომზადების ინტეგრირებულ საბაკალავრო-სამაგისტრო საგანმანათლებლო პროგრამას, სამედიცინო/სტომატოლოგიური განათლების პროგრამას, ბაკალავრიატისა და მაგისტრატურის საგანმანათლებლო პროგრამებს, მაგისტრატურის ან/და დოქტორანტურის საგანმანათლებლო პროგრამებსა და სამეცნიერო კვლევებს ან აკადემიური უმაღლესი განათლების სამივე საფეხურის უმაღლეს საგანმანათლებლო პროგრამებსა და სამეცნიერო კვლევებს; 
    </p>
    <p>
        უსდ-ს სტატუსის მისაღებად აუცილებელია გარე ხარისხის უზრუნველყოფის (ავტორიზაციის) პროცედურის  გავლა. უსდ შეიძლება იყოს როგორც საჯარო, ისე - კერძო სამართლის იურიდიული პირი.
    </p>
    <h2>უმაღლესი განათლების კვალიფიკაციები და ეროვნული კვალიფიკაციების ჩარჩო  </h2>
    <p class="intr-p just">უმაღლესი განათლების საფეხურებისა და ეროვნული კვალიფიკაციების ჩარჩოს მე-5 და მე-8 დონეებთან შესაბამისობა მოცემულია სქემის სახით. საქართველოს ეროვნული კვალიფიკაციების ჩარჩო შემუშავებულია ევროპული კვალიფიკაციების მეტა ჩარჩოებზე (EQF LLL და QF EHEA) დაყრდნობით და არის 8-დონიანი სისტემა, რომელის თითოეული დონე აღწერილია განზოგადებული სწავლის შედეგებით.
    </p>
    <h2>ხარისხის უზრუნველყოფა  </h2>
    <p>საქართველოში მოქმედი უმაღლესი საგანმანათლებლო დაწესებულებები, კანონის თანახმად, უნდა  შეესაბამებოდეს ხარისხის უზრუნველყოფის შიდა და გარე მექანიზმებს.    </p>
    <p>საქართველოს ხარისხის უზრუნველყოფის გარე სისტემა შედგება ავტორიზაციისა და პროგრამული აკრედიტაციისგან, გარე ხარისხის უზრუნველყოფას ახორციელებს სსიპ − განათლების ხარისხის განვითარების ეროვნული ცენტრი.  </p>
    <p>ავტორიზაციის ვადა არის 6 წელი. ავტორიზაცია სავალდებულოა ყველა უსდ-ისთვის, რათა მიეცეთ საშუალება, განახორციელონ საგანმანათლებლო საქმიანობა და გასცენ სახელმწიფოს მიერ აღიარებული კვალიფიკაციის დამადასტურებელი დოკუმენტი. </p>
    <p>საგანმანათლებლო პროგრამის აკრედიტაცია სავალდებულოა და მისი მაქსიმალური ვადაა 7 წელი. საგანმანათლებლო პროგრამას, რომელიც ცენტრს სააკრედიტაციოდ წარედგინა იმ კალენდარული წლის განმავლობაში, როდესაც აღნიშნული საგანმანათლებლო პროგრამის სწავლის სფეროში კლასიფიცირებულ საგანმანათლებლო პროგრამებს ხელახალი აკრედიტაციის გავლა არ უწევს, აკრედიტაცია ენიჭება ამ საგანმანათლებლო პროგრამის სწავლის სფეროში კლასიფიცირებული საგანმანათლებლო პროგრამების აკრედიტაციის მომდევნო ვადამდე.  </p>
    <p>საგანმანათლებლო პროგრამის პირობითი აკრედიტაციის ვადაა არაუმეტეს 4 წელი. საგანმანათლებლო პროგრამა, რომელიც ცენტრში სააკრედიტაციოდ წარდგენილია იმ კალენდარული წლის განმავლობაში, როდესაც აღნიშნული საგანმანათლებლო პროგრამის სწავლის სფეროში კლასიფიცირებულ საგანმანათლებლო პროგრამებს არ უწევთ ხელახალი აკრედიტაცია, სააკრედიტაციოდ წარდგენილ საგანმანათლებლო პროგრამას პირობითი აკრედიტაცია ენიჭება აღნიშნული საგანმანათლებლო პროგრამის სწავლის სფეროში კლასიფიცირებული საგანმანათლებლო პროგრამების აკრედიტაციის ვადამდე, მაგრამ არაუმეტეს 4 წლის ვადისა.  </p>
    <h2>ინფორმაციის ეროვნული წყარო  </h2>


    <p>სსიპ − განათლების ხარისხის განვითარების ეროვნული ცენტრი,<br/>

        ეროვნული საინფორმაციო ცენტრების ევროპული ქსელი ( ENIC) </p>
    <p>მისამართი: მერაბ ალექსიძის ქუჩა, მეორე შესახვევი N 2 </p>
    <p>ელ.ფოსტა <EMAIL>  </p>
    <p>ვებ-გვერდის მისამართი:  www.eqe.ge   </p>
    <p>საქართველოს განათლების, მეცნიერებისა და ახალგაზრდობის სამინისტრო </p>
    <p>მისამართი: დ. უზნაძის ქ. 52, თბილისი, 0102  </p>
    <p>ელექტრონული ფოსტის მისამართი: <EMAIL>   </p>
    <p>ვებ-გვერდის მისამართი:   https://mes.gov.ge/  </p>
</div>




<table class="uniform-table three-column">
    <thead>
    <tr>
        <th>ეროვნული კვალიფიკაციების ჩარჩოს დონე</th>
        <th>უმაღლესი განათლების საფეხური</th>
        <th>კვალიფიკაციები</th>
        <th>ECTS-ის კრედიტები</th>
    </tr>
    </thead>
    <tbody>
    <tr>
        <td>8</td>
        <td>მესამე საფეხური</td>
        <td>დოქტორი</td>
        <td>არანაკლებ 3 წლისა მ.შ. არა უმეტეს 60 კრედიტი სასწავლო კომპონენტი</td>
    </tr>
    <tr>
        <td rowspan="5">7</td>
        <td rowspan="5">მეორე საფეხური</td>
        <td>მაგისტრი;</td>
        <td>60-120</td>
    </tr>
    <tr>
        <td>დიპლომირებული მედიკოსი;</td>
        <td>360</td>
    </tr>
    <tr>
        <td>დიპლომირებული სტომატოლოგი;</td>
        <td>300</td>
    </tr>
    <tr>
        <td>ვეტერინარის მომზადების სერტიფიკატი.</td>
        <td>300</td>
    </tr>
    <tr>
        <td>ვეტერინარიის მაგისტრი; განათლების მაგისტრი;</td>
        <td>60</td>
    </tr>
    <tr>
        <td rowspan="2">6</td>
        <td rowspan="2">პირველი საფეხური</td>
        <td>ბაკალავრის ხარისხი;</td>
        <td>180-240</td>
    </tr>
    <tr>
        <td>მასწავლებლის მომზადების სერტიფიკატი</td>
        <td>60</td>
    </tr>
    <tr>
        <td>5</td>
        <td>მოკლე ციკლი</td>
        <td>ასოცირებული ხარისხი</td>
        <td>120/180</td>
    </tr>
    </tbody>
</table>

<div class="img-cont">
    <img src="{{ asset('images/diploma/footer.png') }}" alt="footerlogo" class="footer-logo"/>
</div>
</body>
</html>
